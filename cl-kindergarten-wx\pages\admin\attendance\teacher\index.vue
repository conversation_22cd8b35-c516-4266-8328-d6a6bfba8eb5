<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">教师考勤管理</text>
				</view>
			</view>
		</view>

		<!-- 日期和统计卡片 -->
		<view class="date-stats-card">
			<view class="date-section">
				<view class="date-nav">
					<view class="date-btn" @click="changeDate(-1)">
						<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
					</view>
					<view class="current-date" @click="showDatePicker">
						<view class="date-picker-content">
							<u-icon name="calendar" color="#ffffff" size="20"></u-icon>
							<text class="date-text">{{ currentDate }}</text>
							<u-icon name="arrow-down" color="#ffffff" size="16"></u-icon>
						</view>
					</view>
					<view class="date-btn" @click="changeDate(1)">
						<u-icon name="arrow-right" color="#ffffff" size="20"></u-icon>
					</view>
				</view>
			</view>

			<view class="stats-section">
				<view class="stat-card total">
					<view class="stat-icon">👨‍🏫</view>
					<view class="stat-info">
						<text class="stat-number">{{ totalTeachers }}</text>
						<text class="stat-label">总人数</text>
					</view>
				</view>
				<view class="stat-card present">
					<view class="stat-icon">✅</view>
					<view class="stat-info">
						<text class="stat-number">{{ presentCount }}</text>
						<text class="stat-label">已签到</text>
					</view>
				</view>
				<view class="stat-card absent">
					<view class="stat-icon">❌</view>
					<view class="stat-info">
						<text class="stat-number">{{ absentCount }}</text>
						<text class="stat-label">未签到</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 批量操作按钮 -->
		<view class="batch-actions" v-if="!isSelectionMode">
			<view class="action-btn batch-btn" @click="enterSelectionMode">
				<u-icon name="checkmark-circle" color="#667eea" size="20"></u-icon>
				<text>批量签到</text>
			</view>
			<view class="action-btn sync-btn" @click="syncFromDingTalk">
				<u-icon name="reload" color="#ffffff" size="20"></u-icon>
				<text>钉钉同步</text>
			</view>
		</view>

		<!-- 选择模式操作栏 -->
		<view class="selection-toolbar" v-if="isSelectionMode">
			<view class="selection-info">
				<text>已选择 {{ selectedTeachers.length }} 人</text>
			</view>
			<view class="selection-actions">
				<view class="toolbar-btn cancel-btn" @click="exitSelectionMode">取消</view>
				<view class="toolbar-btn confirm-btn" @click="batchCheckIn">确认签到</view>
			</view>
		</view>

		<!-- 教师考勤列表 -->
		<view class="attendance-list">
			<!-- 调试信息 -->
			<view style="background: #f0f0f0; padding: 10px; margin: 10px; font-size: 12px; border: 1px solid #ccc;">
				<text style="font-weight: bold;">🔍 调试信息：</text><br/>
				<text>loading: {{ loading }}</text><br/>
				<text>teacherAttendanceData.length: {{ teacherAttendanceData.length }}</text><br/>
				<text>currentPage: {{ currentPage }}</text><br/>
				<text>currentDateParam: {{ currentDateParam }}</text><br/>
				<text>totalTeachers: {{ totalTeachers }}</text><br/>
				<text>presentCount: {{ presentCount }}</text><br/>
				<text>absentCount: {{ absentCount }}</text><br/>
				<text v-if="teacherAttendanceData.length > 0">第一条数据: {{ JSON.stringify(teacherAttendanceData[0]) }}</text>
			</view>

			<!-- 加载状态 -->
			<view v-if="loading" class="loading-container">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 空状态 -->
			<view v-else-if="teacherAttendanceData.length === 0" class="empty-container">
				<text class="empty-icon">📋</text>
				<text class="empty-text">暂无考勤数据</text>
			</view>

			<!-- 教师列表 -->
			<view v-else class="teachers-grid">
				<view
					v-for="teacher in teacherAttendanceData"
					:key="teacher.id"
					class="teacher-card"
					:class="getTeacherCardClass(teacher)"
					@click="handleTeacherClick(teacher)"
				>
					<view class="card-header">
						<view class="teacher-avatar" :class="teacher.status">
							<text class="avatar-emoji">{{ teacher.gender === '男' ? '👨‍🏫' : '👩‍🏫' }}</text>
							<view class="status-indicator" :class="teacher.status"></view>
							<!-- 选择模式下的复选框 -->
							<view v-if="isSelectionMode && teacher.status === 'absent'" class="selection-checkbox" :class="{ checked: selectedTeachers.includes(teacher.id) }">
								<u-icon v-if="selectedTeachers.includes(teacher.id)" name="checkmark" color="#ffffff" size="12"></u-icon>
							</view>
						</view>
						<view class="teacher-info">
							<text class="teacher-name">{{ teacher.name }}</text>
							<text class="teacher-position">{{ teacher.position }}</text>
							<text class="teacher-no">工号: {{ teacher.employeeNo }}</text>
						</view>
						<view class="status-badge" :class="teacher.status">
							<text class="status-text">{{ getStatusText(teacher.status) }}</text>
						</view>
					</view>

					<view class="card-content" v-if="teacher.checkInTime || teacher.checkOutTime">
						<view class="time-info">
							<view class="time-item" v-if="teacher.checkInTime">
								<view class="time-icon">🌅</view>
								<view class="time-details">
									<text class="time-label">签到</text>
									<text class="time-value">{{ teacher.checkInTime }}</text>
								</view>
							</view>
							<view class="time-item" v-if="teacher.checkOutTime">
								<view class="time-icon">🌇</view>
								<view class="time-details">
									<text class="time-label">签退</text>
									<text class="time-value">{{ teacher.checkOutTime }}</text>
								</view>
							</view>
						</view>
					</view>

					<view class="card-content" v-else-if="teacher.status === 'leave'">
						<view class="leave-info">
							<view class="leave-icon">🏖️</view>
							<text class="leave-reason">{{ teacher.leaveReason || '请假' }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载更多提示 -->
			<view v-if="teacherAttendanceData.length > 0" class="load-more">
				<view v-if="loading" class="loading-more">
					<view class="loading-spinner"></view>
					<text>加载中...</text>
				</view>
				<view v-else-if="!hasMore" class="no-more">
					<text>没有更多数据了</text>
				</view>
				<view v-else class="pull-up">
					<text>上拉加载更多</text>
				</view>
			</view>
		</view>

		<!-- 自定义日期选择器 -->
		<CustomDatePicker
			:show.sync="showPicker"
			:value="pickerValue"
			title="选择考勤日期"
			@confirm="onDateConfirm"
			@cancel="showPicker = false"
		/>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'
import CustomDatePicker from '@/components/CustomDatePicker/CustomDatePicker.vue'
import { 
	getTeacherAttendanceOverview, 
	getAllTeacherList,
	batchTeacherCheckin,
	batchConfirmAttendance 
} from '@/api/teacherAttendance.js'

export default {
	components: {
		CustomDatePicker
	},
	data() {
		return {
			currentDate: '',
			currentDateParam: '',
			totalTeachers: 0,
			presentCount: 0,
			absentCount: 0,
			teacherAttendanceData: [],
			loading: false,
			// 日期选择器相关
			showPicker: false,
			pickerValue: '',
			// 批量选择相关
			isSelectionMode: false,
			selectedTeachers: [],
			// 分页相关
			currentPage: 1,
			pageSize: 10,
			hasMore: true
		}
	},
	onLoad() {
		this.initCurrentDate()
		this.loadAttendanceData()
	},

	// 触底加载更多
	onReachBottom() {
		this.loadMore()
	},
	methods: {
		// 获取教师卡片的动态class
		getTeacherCardClass(teacher) {
			const classes = {
				'selection-mode': this.isSelectionMode,
				'selected': this.selectedTeachers.includes(teacher.id),
				'selectable': this.isSelectionMode && teacher.status === 'absent'
			}
			// 动态添加状态class
			if (teacher.status) {
				classes[teacher.status] = true
			}
			return classes
		},

		async loadAttendanceData(isLoadMore = false) {
			if (this.loading) return
			this.loading = true

			try {
				// 如果不是加载更多，重置分页
				if (!isLoadMore) {
					this.currentPage = 1
					this.teacherAttendanceData = []
				}

				// 获取考勤列表数据（包含统计信息）
				const requestParams = {
					attendanceDate: this.currentDateParam,
					pageNum: this.currentPage,
					pageSize: this.pageSize
				}
				console.log('请求参数:', requestParams)

				const listRes = await getTeacherAttendanceOverview(requestParams)

				console.log('🔍 后端返回数据:', listRes)
				console.log('🔍 后端返回状态码:', listRes.code)
				console.log('🔍 后端返回成功标志:', listRes.success)

				if (listRes.code === 200 || listRes.success) {
					console.log('✅ 接口调用成功，开始处理数据')
					// 后端返回的是listRes.rows
					const rawList = listRes.rows || []
					console.log('原始列表数据:', rawList)
					console.log('原始列表数据长度:', rawList.length)
					console.log('后端返回的total:', listRes.total)

					// 更新统计数据
					this.totalTeachers = listRes.total || rawList.length || 0

					// 判断是否还有更多数据
					this.hasMore = rawList.length >= this.pageSize

					// 正确处理复杂的数据结构，从dingtalkRecords和manualRecords中提取考勤数据
					const newData = rawList.map((item, index) => {
						// 从dingtalkRecords和manualRecords中提取考勤时间
						const attendanceInfo = this.extractAttendanceInfo(item)

						// 调试信息
						if (index < 3) { // 只打印前3条记录的详细信息
							console.log(`教师 ${item.teacherName} 的考勤信息:`)
							console.log('- dingtalkRecords:', item.dingtalkRecords)
							console.log('- manualRecords:', item.manualRecords)
							console.log('- 提取的考勤信息:', attendanceInfo)
						}

						return {
							id: item.teacherId || item.id,
							attendanceId: item.attendanceId,
							name: item.teacherName,
							employeeNo: item.teacherCode || item.employeeNo,
							position: item.position || '教师',
							gender: item.gender === '0' ? '男' : (item.gender === '1' ? '女' : (item.gender || '未知')),
							checkInTime: attendanceInfo.checkInTime,
							checkOutTime: attendanceInfo.checkOutTime,
							status: attendanceInfo.status,
							leaveReason: item.leaveReason || item.remark || '',
							attendanceStatus: item.attendanceStatus || '0',
							attendanceDate: item.attendanceDate,
							// 保留原始记录用于调试
							dingtalkRecords: item.dingtalkRecords || [],
							manualRecords: item.manualRecords || []
						}
					})
					console.log('🔄 处理后的数据:', newData)
					console.log('🔄 处理后的数据长度:', newData.length)

					console.log('🔄 准备更新 teacherAttendanceData，当前长度:', this.teacherAttendanceData.length)

					if (isLoadMore) {
						this.teacherAttendanceData = [...this.teacherAttendanceData, ...newData]
						console.log('📝 加载更多模式，合并数据')
					} else {
						this.teacherAttendanceData = newData
						this.calculateStats(rawList)
						console.log('📝 首次加载模式，替换数据')
					}

					console.log('✅ 最终的 teacherAttendanceData:', this.teacherAttendanceData)
					console.log('✅ 最终的 teacherAttendanceData 长度:', this.teacherAttendanceData.length)
					console.log('✅ Vue响应式检查 - loading状态:', this.loading)
					console.log('✅ Vue响应式检查 - 是否显示空状态:', this.teacherAttendanceData.length === 0)

					// 使用$nextTick确保DOM更新，然后强制刷新视图
					this.$nextTick(() => {
						console.log('🔄 $nextTick 执行，准备强制更新视图')
						console.log('🔄 更新前 teacherAttendanceData.length:', this.teacherAttendanceData.length)
						this.$forceUpdate()
						console.log('✅ 视图已强制更新')
					})
				} else {
					console.log('接口返回失败:', listRes)
					toast(listRes.msg || listRes.message || '获取数据失败')
				}

			} catch (error) {
				console.error('加载考勤数据失败:', error)
				toast('加载数据失败，请重试')
			} finally {
				this.loading = false
			}
		},

		// 加载更多数据
		async loadMore() {
			if (!this.hasMore || this.loading) return

			this.currentPage++
			await this.loadAttendanceData(true)
		},
		
		calculateStats(data) {
			let present = 0
			let absent = 0

			data.forEach(item => {
				// 使用extractAttendanceInfo来正确判断考勤状态
				const attendanceInfo = this.extractAttendanceInfo(item)
				if (attendanceInfo.status === 'present') {
					present++
				} else if (attendanceInfo.status === 'absent') {
					absent++
				}
				// 请假状态不计入出勤或缺勤统计
			})

			this.presentCount = present
			this.absentCount = absent

			console.log('统计结果 - 出勤:', present, '缺勤:', absent)
		},
		

		
		// 从dingtalkRecords和manualRecords中提取考勤信息
		extractAttendanceInfo(item) {
			let checkInTime = ''
			let checkOutTime = ''
			let status = 'absent'

			// 如果有请假原因，直接返回请假状态
			if (item.leaveReason) {
				return {
					checkInTime: '',
					checkOutTime: '',
					status: 'leave'
				}
			}

			// 合并所有考勤记录（钉钉记录 + 手动记录）
			const allRecords = []

			// 处理钉钉记录
			if (item.dingtalkRecords && Array.isArray(item.dingtalkRecords)) {
				item.dingtalkRecords.forEach(record => {
					if (record.checkTime) {
						allRecords.push({
							time: record.checkTime,
							type: record.checkType, // OnDuty/OffDuty
							source: 'dingtalk'
						})
					}
				})
			}

			// 处理手动记录
			if (item.manualRecords && Array.isArray(item.manualRecords)) {
				item.manualRecords.forEach(record => {
					if (record.checkInTime) {
						allRecords.push({
							time: record.checkInTime,
							type: 'OnDuty',
							source: 'manual'
						})
					}
					if (record.checkOutTime) {
						allRecords.push({
							time: record.checkOutTime,
							type: 'OffDuty',
							source: 'manual'
						})
					}
				})
			}

			// 如果直接有checkInTime/checkOutTime字段，也要考虑
			if (item.checkInTime) {
				allRecords.push({
					time: item.checkInTime,
					type: 'OnDuty',
					source: 'direct'
				})
			}
			if (item.checkOutTime) {
				allRecords.push({
					time: item.checkOutTime,
					type: 'OffDuty',
					source: 'direct'
				})
			}

			// 从所有记录中提取签到和签退时间
			const onDutyRecords = allRecords.filter(r => r.type === 'OnDuty').sort((a, b) => new Date(a.time) - new Date(b.time))
			const offDutyRecords = allRecords.filter(r => r.type === 'OffDuty').sort((a, b) => new Date(a.time) - new Date(b.time))

			// 取最早的签到时间
			if (onDutyRecords.length > 0) {
				checkInTime = this.formatTime(onDutyRecords[0].time)
				status = 'present'
			}

			// 取最晚的签退时间
			if (offDutyRecords.length > 0) {
				checkOutTime = this.formatTime(offDutyRecords[offDutyRecords.length - 1].time)
			}

			return {
				checkInTime,
				checkOutTime,
				status
			}
		},

		getAttendanceStatus(item) {
			// 这个方法现在由extractAttendanceInfo替代，保留用于兼容性
			if (item.leaveReason) {
				return 'leave'
			} else if (item.checkInTime) {
				return 'present'
			} else {
				return 'absent'
			}
		},

		formatTime(timeString) {
			if (!timeString) return ''

			try {
				let date

				// 处理多种时间格式
				if (typeof timeString === 'string') {
					// 处理ISO格式、时间戳字符串等
					if (timeString.includes('T') || timeString.includes('-')) {
						date = new Date(timeString)
					} else if (/^\d+$/.test(timeString)) {
						// 纯数字字符串，当作时间戳处理
						date = new Date(parseInt(timeString))
					} else {
						date = new Date(timeString)
					}
				} else if (typeof timeString === 'number') {
					// 数字时间戳
					date = new Date(timeString)
				} else {
					date = new Date(timeString)
				}

				// 检查日期是否有效
				if (isNaN(date.getTime())) {
					console.warn('无效的时间格式:', timeString)
					return ''
				}

				return date.toLocaleTimeString('zh-CN', {
					hour12: false,
					hour: '2-digit',
					minute: '2-digit'
				})
			} catch (error) {
				console.error('时间格式化错误:', error, timeString)
				return ''
			}
		},

		initCurrentDate() {
			const now = new Date()
			const year = now.getFullYear()
			const month = String(now.getMonth() + 1).padStart(2, '0')
			const day = String(now.getDate()).padStart(2, '0')
			const weekdays = ['日', '一', '二', '三', '四', '五', '六']
			const weekday = weekdays[now.getDay()]
			this.currentDate = `${year}年${month}月${day}日 周${weekday}`
			this.currentDateParam = `${year}-${month}-${day}`
			// 初始化日期选择器的值
			this.pickerValue = now.getTime()
		},
		
		goBack() {
			uni.navigateBack()
		},

		// 显示日期选择器
		showDatePicker() {
			this.showPicker = true
		},

		// 日期选择确认
		onDateConfirm(e) {
			const selectedDate = new Date(e.value)
			const year = selectedDate.getFullYear()
			const month = String(selectedDate.getMonth() + 1).padStart(2, '0')
			const day = String(selectedDate.getDate()).padStart(2, '0')
			const weekdays = ['日', '一', '二', '三', '四', '五', '六']
			const weekday = weekdays[selectedDate.getDay()]

			this.currentDate = `${year}年${month}月${day}日 周${weekday}`
			this.currentDateParam = `${year}-${month}-${day}`
			this.pickerValue = e.value
			this.showPicker = false

			// 重新加载数据
			this.loadAttendanceData()
		},


		
		changeDate(direction) {
			const currentDate = new Date(this.currentDateParam)
			currentDate.setDate(currentDate.getDate() + direction)
			
			const year = currentDate.getFullYear()
			const month = String(currentDate.getMonth() + 1).padStart(2, '0')
			const day = String(currentDate.getDate()).padStart(2, '0')
			const weekdays = ['日', '一', '二', '三', '四', '五', '六']
			const weekday = weekdays[currentDate.getDay()]
			
			this.currentDate = `${year}年${month}月${day}日 周${weekday}`
			this.currentDateParam = `${year}-${month}-${day}`
			
			// 重新加载数据
			this.loadAttendanceData()
		},
		
		getStatusText(status) {
			const statusMap = {
				present: '已签到',
				absent: '未签到',
				leave: '请假'
			}
			return statusMap[status] || '未知'
		},

		// 进入选择模式
		enterSelectionMode() {
			this.isSelectionMode = true
			this.selectedTeachers = []
		},

		// 退出选择模式
		exitSelectionMode() {
			this.isSelectionMode = false
			this.selectedTeachers = []
		},

		// 处理教师卡片点击
		handleTeacherClick(teacher) {
			if (this.isSelectionMode && teacher.status === 'absent') {
				this.toggleTeacherSelection(teacher.id)
			}
		},

		// 切换教师选择状态
		toggleTeacherSelection(teacherId) {
			const index = this.selectedTeachers.indexOf(teacherId)
			if (index > -1) {
				this.selectedTeachers.splice(index, 1)
			} else {
				this.selectedTeachers.push(teacherId)
			}
		},

		// 批量签到
		async batchCheckIn() {
			if (this.selectedTeachers.length === 0) {
				toast('请选择需要签到的教师')
				return
			}

			uni.showModal({
				title: '确认签到',
				content: `确定为选中的 ${this.selectedTeachers.length} 名教师签到吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({ title: '签到中...' })

							// 模拟批量签到API调用
							const result = await new Promise(resolve => {
								setTimeout(() => {
									resolve({ code: 200, msg: '批量签到成功' })
								}, 1500)
							})

							if (result.code === 200) {
								toast(`已为 ${this.selectedTeachers.length} 名教师签到`)
								// 重新加载数据
								await this.loadAttendanceData()
								// 退出选择模式
								this.exitSelectionMode()
							} else {
								toast(result.msg || '批量签到失败')
							}

						} catch (error) {
							console.error('批量签到失败:', error)
							toast('批量签到失败，请重试')
						} finally {
							uni.hideLoading()
						}
					}
				}
			})
		},

		// 钉钉同步
		async syncFromDingTalk() {
			uni.showModal({
				title: '钉钉同步',
				content: '确定要从钉钉同步今日考勤数据吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({ title: '同步中...' })

							// 模拟钉钉同步API调用
							const result = await new Promise(resolve => {
								setTimeout(() => {
									resolve({ code: 200, msg: '同步成功', data: { syncCount: 15 } })
								}, 2000)
							})

							if (result.code === 200) {
								toast(`同步成功，更新了 ${result.data.syncCount} 条考勤记录`)
								// 重新加载数据
								await this.loadAttendanceData()
							} else {
								toast(result.msg || '同步失败')
							}

						} catch (error) {
							console.error('钉钉同步失败:', error)
							toast('同步失败，请重试')
						} finally {
							uni.hideLoading()
						}
					}
				}
			})
		}

	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	display: block;
	font-size: 36rpx;
	font-weight: 700;
	color: #ffffff;
	margin-bottom: 8rpx;
}

.subtitle-text {
	display: block;
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	font-weight: 400;
}

/* 日期统计卡片 */
.date-stats-card {
	margin: 30rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 32rpx;
	padding: 40rpx;
	box-shadow: 0 16rpx 48rpx rgba(102, 126, 234, 0.25);
}

.date-section {
	margin-bottom: 40rpx;
}

.date-nav {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 40rpx;
}

.date-btn {
	width: 72rpx;
	height: 72rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.25);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	backdrop-filter: blur(10rpx);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

	&:active {
		transform: scale(0.95);
		background: rgba(255, 255, 255, 0.35);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
	}
}

.current-date {
	text-align: center;
	flex: 1;
}

.date-picker-content {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	padding: 20rpx 30rpx;
	border-radius: 50rpx;
	background: rgba(102, 126, 234, 0.05);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
		background: rgba(102, 126, 234, 0.1);
	}
}

.date-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
}

.stats-section {
	display: flex;
	gap: 24rpx;
	margin-top: 40rpx;
}

.stat-card {
	flex: 1;
	padding: 30rpx 20rpx;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
	}

	&.total {
		background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
		&::before { background: linear-gradient(90deg, #2196f3 0%, #1976d2 100%); }
	}

	&.present {
		background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
		&::before { background: linear-gradient(90deg, #4caf50 0%, #388e3c 100%); }
	}

	&.absent {
		background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
		&::before { background: linear-gradient(90deg, #f44336 0%, #d32f2f 100%); }
	}
}

.stat-icon {
	font-size: 32rpx;
}

.stat-info {
	text-align: center;
}

.stat-number {
	display: block;
	font-size: 36rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #666666;
	font-weight: 500;
}

/* 批量操作按钮 */
.batch-actions {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	background: #f8f9fa;
}

.action-btn {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	padding: 24rpx;
	border-radius: 24rpx;
	font-size: 28rpx;
	font-weight: 600;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
	}
}

.batch-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #ffffff;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.sync-btn {
	background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
	color: #ffffff;
	box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.3);
}

/* 选择模式工具栏 */
.selection-toolbar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	background: #667eea;
	color: #ffffff;
}

.selection-info {
	font-size: 28rpx;
	font-weight: 600;
}

.selection-actions {
	display: flex;
	gap: 20rpx;
}

.toolbar-btn {
	padding: 16rpx 32rpx;
	border-radius: 50rpx;
	font-size: 26rpx;
	font-weight: 600;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
	}
}

.cancel-btn {
	background: rgba(255, 255, 255, 0.2);
	color: #ffffff;
}

.confirm-btn {
	background: #ffffff;
	color: #667eea;
}

/* 考勤列表 */
.attendance-list {
	padding: 0 30rpx 200rpx;
}

/* 加载更多 */
.load-more {
	padding: 40rpx 0;
	text-align: center;
}

.loading-more {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	color: #667eea;
	font-size: 28rpx;
}

.no-more {
	color: #999999;
	font-size: 26rpx;
}

.pull-up {
	color: #cccccc;
	font-size: 26rpx;
}

/* 加载更多 */
.load-more {
	padding: 40rpx 0;
	text-align: center;
}

.loading-more {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	color: #667eea;
	font-size: 28rpx;
}

.no-more {
	color: #999999;
	font-size: 26rpx;
}

.pull-up {
	color: #cccccc;
	font-size: 26rpx;
}

/* 加载状态 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
	gap: 20rpx;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #667eea;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}

/* 空状态 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
	gap: 20rpx;
}

.empty-icon {
	font-size: 80rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999999;
}

.list-header {
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.header-icon {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
}

.header-info {
	flex: 1;
}

.header-title {
	display: block;
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.header-subtitle {
	display: block;
	font-size: 24rpx;
	color: #666666;
}

/* 教师网格 */
.teachers-grid {
	display: grid;
	grid-template-columns: 1fr;
	gap: 32rpx;
}

.teacher-card {
	background: #ffffff;
	border-radius: 28rpx;
	padding: 32rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
	border: 1rpx solid rgba(0, 0, 0, 0.04);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 4rpx;
		background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
		opacity: 0;
		transition: opacity 0.3s ease;
	}

	&:active {
		transform: translateY(-2rpx);
		box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
	}

	&.present::before {
		background: linear-gradient(90deg, #4caf50 0%, #388e3c 100%);
		opacity: 1;
	}

	&.absent::before {
		background: linear-gradient(90deg, #f44336 0%, #d32f2f 100%);
		opacity: 1;
	}

	&.leave::before {
		background: linear-gradient(90deg, #ff9800 0%, #f57c00 100%);
		opacity: 1;
	}

	&.selection-mode {
		border: 2rpx solid #e0e0e0;

		&.selectable {
			border-color: #667eea;
			background: rgba(102, 126, 234, 0.02);
		}

		&.selected {
			border-color: #667eea;
			background: rgba(102, 126, 234, 0.05);
			box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.15);
		}
	}
}

.card-header {
	display: flex;
	align-items: center;
	gap: 20rpx;
	margin-bottom: 24rpx;
}

.teacher-avatar {
	width: 90rpx;
	height: 90rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	font-size: 36rpx;

	&.present {
		background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
	}

	&.absent {
		background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
	}

	&.leave {
		background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
	}
}

.avatar-emoji {
	font-size: 36rpx;
}

.status-indicator {
	position: absolute;
	bottom: 6rpx;
	right: 6rpx;
	width: 24rpx;
	height: 24rpx;
	border-radius: 50%;
	border: 3rpx solid #ffffff;

	&.present { background: #4caf50; }
	&.absent { background: #f44336; }
	&.leave { background: #ff9800; }
}

.selection-checkbox {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	background: #667eea;
	border: 3rpx solid #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
	transition: all 0.3s ease;

	&.checked {
		background: #4caf50;
		transform: scale(1.1);
	}
}

.teacher-basic {
	flex: 1;
}

.teacher-name {
	display: block;
	font-size: 34rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.teacher-position {
	display: block;
	font-size: 26rpx;
	color: #667eea;
	font-weight: 600;
	margin-bottom: 6rpx;
}

.teacher-no {
	display: block;
	font-size: 22rpx;
	color: #999999;
}

.quick-actions {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: #e9ecef;
	}
}

.card-content {
	margin-top: 24rpx;
	padding-top: 24rpx;
	border-top: 1rpx solid rgba(0, 0, 0, 0.06);
}

.time-info {
	display: flex;
	gap: 32rpx;
}

.time-item {
	display: flex;
	align-items: center;
	gap: 12rpx;
	flex: 1;
}

.time-icon {
	font-size: 28rpx;
}

.time-details {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.time-label {
	font-size: 22rpx;
	color: #999999;
	font-weight: 500;
}

.time-value {
	font-size: 28rpx;
	color: #333333;
	font-weight: 600;
}

.leave-info {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 16rpx 20rpx;
	background: rgba(255, 152, 0, 0.1);
	border-radius: 16rpx;
}

.leave-icon {
	font-size: 28rpx;
}

.leave-reason {
	font-size: 26rpx;
	color: #ef6c00;
	font-weight: 500;
}

.time-section {
	flex: 1;
}

.time-row {
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.time-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.time-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
	background: rgba(102, 126, 234, 0.1);
}

.time-details {
	flex: 1;
}

.time-label {
	display: block;
	font-size: 22rpx;
	color: #666666;
	margin-bottom: 4rpx;
}

.time-value {
	display: block;
	font-size: 26rpx;
	font-weight: 600;
	color: #333333;

	&.absent {
		color: #f44336;
	}
}

.status-section {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 16rpx;
}

.status-badge {
	padding: 16rpx 24rpx;
	border-radius: 50rpx;
	font-weight: 600;
	font-size: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

	&.present {
		background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
		color: #2e7d32;
	}

	&.absent {
		background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
		color: #c62828;
	}

	&.leave {
		background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
		color: #ef6c00;
	}
}

.status-text {
	font-size: 22rpx;
	font-weight: 600;

	.status-badge.present & { color: #4caf50; }
	.status-badge.absent & { color: #f44336; }
	.status-badge.leave & { color: #ff9800; }
}

.work-hours {
	text-align: right;
}

.hours-label {
	display: block;
	font-size: 20rpx;
	color: #999999;
	margin-bottom: 4rpx;
}

.hours-value {
	display: block;
	font-size: 24rpx;
	font-weight: 600;
	color: #667eea;
}

/* 浮动操作按钮 */
.floating-actions {
	position: fixed;
	bottom: 40rpx;
	left: 50%;
	transform: translateX(-50%);
	width: calc(100% - 60rpx);
	z-index: 100;
}

.fab-container {
	display: flex;
	gap: 20rpx;
}

.fab-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	box-shadow: 0 16rpx 40rpx rgba(102, 126, 234, 0.4);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		transition: left 0.5s;
	}

	&:active {
		transform: translateY(4rpx) scale(0.98);
		box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.6);

		&::before {
			left: 100%;
		}
	}

	&.active {
		background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
		box-shadow: 0 16rpx 40rpx rgba(40, 167, 69, 0.4);

		&:active {
			box-shadow: 0 8rpx 20rpx rgba(40, 167, 69, 0.6);
		}
	}
}

.fab-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 选择模式样式 */
.teacher-card {
	&.selection-mode {
		&.selectable {
			border: 2rpx solid rgba(102, 126, 234, 0.3);

			&:active {
				transform: scale(0.98);
			}

			&.selected {
				border-color: #667eea;
				background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
			}
		}

		&:not(.selectable) {
			opacity: 0.6;
			pointer-events: none;
		}
	}
}

.selection-checkbox {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	background: #ffffff;
	border: 3rpx solid #e0e0e0;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&.checked {
		background: #667eea;
		border-color: #667eea;
		transform: scale(1.1);
	}
}

.selection-tip {
	position: fixed;
	top: 200rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 99;
}

.tip-content {
	background: rgba(0, 0, 0, 0.8);
	color: #ffffff;
	padding: 20rpx 30rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	gap: 16rpx;
	backdrop-filter: blur(10rpx);
	animation: tipFadeIn 0.3s ease;
}

.tip-text {
	font-size: 26rpx;
	font-weight: 500;
}

.tip-actions {
	margin-left: 20rpx;
}

.cancel-selection {
	font-size: 24rpx;
	color: #ff6b6b;
	font-weight: 600;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	background: rgba(255, 107, 107, 0.2);
}

@keyframes tipFadeIn {
	from {
		opacity: 0;
		transform: translateX(-50%) translateY(-20rpx);
	}
	to {
		opacity: 1;
		transform: translateX(-50%) translateY(0);
	}
}
</style>
